from typing import Any, List, Dict
from datetime import datetime
import logging
import time

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session

from app import crud, schemas
from app.api.deps import get_db, get_current_admin_user, get_current_user_from_clerk
from app.db.session import SessionLocal
from app.models.user import User
from app.models.scraper_activity import ScraperActivity
from app.services.scrapers.tasks import (
    scrape_agenz_urls,
    scrape_sarouty_urls,
    LLMProcessor
)
from app.services.scrapers.base_scraper import redis_client
from app.services.scrapers.agenz import AgenzScraper
from app.services.scrapers.sarouty import SaroutyScraper
from app.services.scrapers.avito_rent import AvitoRentScraper
from app.services.scrapers.avito_sale import AvitoSaleScraper
from app.services.scrapers.llm_service import (
    is_rate_limited, get_llm_queue_status, send_llm_queue_control_command, 
    set_llm_queue_status
)

router = APIRouter()
logger = logging.getLogger(__name__)

# Simple dict to track scraper status without using deprecated modules
scraper_status = {}

def set_scraper_status(scraper_name, status, **kwargs):
    """Set the status of a scraper."""
    scraper_status[scraper_name] = {
        "status": status,
        "timestamp": datetime.now(),
        **kwargs
    }

def get_scraper_status(scraper_name):
    """Get the status of a scraper."""
    return scraper_status.get(scraper_name, {"status": "idle"})

@router.delete("/clear/{source}", response_model=schemas.ScraperRunResponse)
def clear_source(
    *,
    db: Session = Depends(get_db),
    source: str,
    current_user: User = Depends(get_current_admin_user),
) -> Any:
    """
    Clear all listings from a specific source.
    Admin only.
    """
    count = crud.listing.clear_by_source(db=db, source=source)
    
    return {
        "success": True,
        "message": f"Cleared {count} listings from {source}"
    }


@router.post("/{scraper_name}/run", response_model=Dict[str, Any])
def run_scraper_endpoint(
    scraper_name: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
) -> Any:
    """Run a scraper by setting its state to running.
    Admin only.
    
    Args:
        scraper_name: Name of the scraper to run
        background_tasks: FastAPI background tasks
        db: Database session
        current_user: Current authenticated admin user
        
    Returns:
        Status of the scraper operation
    """
    # Check if scraper is valid using the registry
    if not is_valid_scraper(scraper_name):
        raise HTTPException(status_code=400, detail=f"Unsupported scraper: {scraper_name}")

    # Get status key
    status_key = f"{scraper_name}_status"
    
    # Check if scraper is actually running by checking Redis status directly
    # and ensuring there are active queues (not just a stale status flag)
    try:
        # Create appropriate scraper instance using the registry to check queue activity
        scraper_config = get_scraper_from_registry(scraper_name)
        scraper_class = scraper_config["scraper_class"]
        db_temp = SessionLocal()
        scraper = scraper_class(db_temp)
        
        # Get queue lengths to check if scraper is actually active
        queue_stats = scraper.get_all_queue_lengths()
        total_items = sum(queue_stats.values())
        
        # Get Redis status
        current_redis_status = redis_client.get(status_key)
        redis_status = current_redis_status.decode('utf-8') if current_redis_status else "idle"
        
        db_temp.close()
        
        # Only prevent restart if Redis status is "running" AND there are actually items being processed
        # This allows restarting "stuck" scrapers that are marked as running but not actually working
        if redis_status == "running" and total_items > 0:
            return {"status": "already_running", "message": f"Scraper '{scraper_name}' is already running and processing {total_items} items"}
    except Exception as e:
        logger.warning(f"Error checking scraper activity for {scraper_name}: {e}")
        # If we can't check the status, allow the restart
    
    try:
        # Check if URL collection is marked as completed
        url_collection_completed = redis_client.get(f"{scraper_name.lower()}_url_collection_completed")
        if url_collection_completed:
            # Reset URL collection status
            redis_client.delete(f"{scraper_name.lower()}_url_collection_completed")
            # Reset the last page to 1
            redis_client.set(f"{scraper_name.lower()}_last_page", "1")
            logger.info(f"Reset URL collection status for {scraper_name}")
        
        # Set scraper status to running in Redis
        redis_client.set(status_key, "running")
        
        # Set local status
        set_scraper_status(scraper_name, "running", message=f"Scraper {scraper_name} started")
        
        # Create activity record
        db_activity = ScraperActivity(
            scraper_name=scraper_name,
            status="started",
            message=f"Started {scraper_name} scraper",
            start_time=datetime.utcnow()
        )
        db.add(db_activity)
        db.commit()
        
        return {"status": "started", "message": f"Scraper '{scraper_name}' started"}
        
    except Exception as e:
        logger.exception(f"Error starting {scraper_name}")
        return {"status": "error", "message": f"Error starting scraper: {str(e)}"}


@router.post("/{scraper_name}/stop", response_model=Dict[str, Any])
def stop_scraper(
    scraper_name: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
) -> Any:
    """Stop a running scraper by setting its state to stopped.
    Admin only.
    
    Args:
        scraper_name: Name of the scraper to stop
        db: Database session
        current_user: Current authenticated admin user
        
    Returns:
        Status of the scraper operation
    """
    # Check if scraper is valid using the registry
    if not is_valid_scraper(scraper_name):
        raise HTTPException(status_code=400, detail=f"Unsupported scraper: {scraper_name}")
    
    try:
        # Get status key
        status_key = f"{scraper_name}_status"
        
        # Set scraper status to stopped in Redis
        redis_client.set(status_key, "stopped")
        
        # Set local status
        set_scraper_status(scraper_name, "stopped", message="Scraper stopped")
        
        # Create a scraper instance using the registry
        scraper_config = get_scraper_from_registry(scraper_name)
        scraper_class = scraper_config["scraper_class"]
        scraper = scraper_class(db)
        
        # Purge only scraper-specific queues, preserving LLM processing queues
        scraper.purge_scraper_queues_only()
        
        # Create activity record
        db_activity = ScraperActivity(
            scraper_name=scraper_name,
            status="stopped",
            message=f"Stopped {scraper_name} scraper",
            start_time=datetime.utcnow()
        )
        db.add(db_activity)
        db.commit()
        
        return {
            "status": "stopped", 
            "message": f"Scraper '{scraper_name}' stopped. Details and processed queues preserved for LLM processing."
        }
    
    except Exception as e:
        logger.exception(f"Error stopping scraper {scraper_name}")
        return {
            "status": "error",
            "message": f"Error stopping scraper: {str(e)}"
        }


@router.get("/{scraper_name}/status", response_model=Dict[str, Any])
def get_scraper_status_endpoint(
    scraper_name: str,
    current_user: User = Depends(get_current_user_from_clerk)
) -> Any:
    """Get the status of a scraper.
    Requires authentication.
    
    Args:
        scraper_name: Name of the scraper
        current_user: Current authenticated user
        
    Returns:
        Status of the scraper
    """
    if not is_valid_scraper(scraper_name):
        # For invalid scrapers, provide a default response
        return {"status": "not_implemented", "message": f"Scraper '{scraper_name}' not implemented"}
    
    # Initialize the scraper to use its queue methods
    db = SessionLocal()
    try:
        # Create appropriate scraper instance using the registry
        scraper_config = get_scraper_from_registry(scraper_name)
        scraper_class = scraper_config["scraper_class"]
        scraper = scraper_class(db)
        
        # Get queue lengths using BaseScraper methods
        queue_stats = scraper.get_all_queue_lengths()
        
        # Get actual queue lengths (excluding empty queue markers)
        url_queue_size = queue_stats.get('url_queue', 0)
        details_queue_size = queue_stats.get('details_queue', 0)
        processed_queue_size = queue_stats.get('processed_queue', 0)
        
        # Check for empty queue markers and adjust sizes
        if url_queue_size > 0:
            # Check if first item is a marker
            first_item = redis_client.lindex(f"{scraper_name.lower()}_url_queue", 0)
            if first_item in [b'', b'__QUEUE_MARKER__']:
                url_queue_size -= 1
                
        if details_queue_size > 0:
            # Check if first item is a marker
            first_item = redis_client.lindex(f"{scraper_name.lower()}_details_queue", 0)
            if first_item in [b'', b'__QUEUE_MARKER__']:
                details_queue_size -= 1
                
        if processed_queue_size > 0:
            # Check if first item is a marker
            first_item = redis_client.lindex(f"{scraper_name.lower()}_processed_queue", 0)
            if first_item in [b'', b'__QUEUE_MARKER__']:
                processed_queue_size -= 1
        
        # Total items in the queue
        total_items = url_queue_size + details_queue_size + processed_queue_size
        
        # Get the latest activity to determine total URLs and processed URLs
        latest_activity = db.query(ScraperActivity).filter(
            ScraperActivity.scraper_name == scraper_name
        ).order_by(ScraperActivity.start_time.desc()).first()
        
        listings_added = 0
        if latest_activity and latest_activity.status in ['running', 'starting']:
            listings_added = latest_activity.listings_added or 0
        
        # Calculate progress
        total_urls = listings_added + total_items
        processed_urls = listings_added
        
        progress = 0
        if total_urls > 0:
            progress = (processed_urls / total_urls) * 100
        
        # Map queue names to match frontend expectations
        # Note: processor_queue now shows listings awaiting LLM processing (details queue)
        # extractor_queue is removed since details extraction feeds directly into LLM processing
        frontend_queue_stats = {
            "collector_queue": url_queue_size,
            "extractor_queue": 0,  # No longer used - details extraction feeds directly to LLM
            "processor_queue": details_queue_size,  # Listings awaiting LLM processing
            "total_urls": total_urls,
            "processed_urls": processed_urls,
            "progress": progress
        }
        
        # Get scraper status from Redis
        status_key = f"{scraper_name}_status"
        current_status = redis_client.get(status_key)
        status = current_status.decode('utf-8') if current_status else "idle"
        
        # Get queue management information
        queue_management_info = {}
        try:
            from app.services.scrapers.queue_manager import queue_limit_manager, get_queue_management_status
            
            # Get current queue management status
            queue_mgmt_status = get_queue_management_status()
            paused_scrapers = queue_mgmt_status.get("paused_scrapers", [])
            
            # Check if this scraper is paused due to queue limits
            is_paused_for_queue = scraper_name in paused_scrapers
            
            queue_management_info = {
                "is_paused_for_queue_limits": is_paused_for_queue,
                "total_queue_size": queue_mgmt_status.get("total_queue_size", 0),
                "queue_limit": queue_mgmt_status.get("max_limit", 1500),
                "resume_threshold": queue_mgmt_status.get("resume_threshold", 750),
                "memory_pressure": queue_mgmt_status.get("memory_pressure", False),
                "memory_info": queue_mgmt_status.get("memory_info", {})
            }
            
            # Override status if paused for queue limits
            if is_paused_for_queue and status == "running":
                status = "paused_for_queue_limits"
                set_scraper_status(scraper_name, "paused_for_queue_limits", 
                                 message=f"Paused due to queue size ({queue_mgmt_status.get('total_queue_size', 0)}) or memory pressure")
        
        except Exception as e:
            logger.error(f"Error getting queue management info for {scraper_name}: {str(e)}")
            queue_management_info = {
                "is_paused_for_queue_limits": False,
                "total_queue_size": 0,
                "queue_limit": 1500,
                "resume_threshold": 750,
                "memory_pressure": False,
                "memory_info": {}
            }
        
        # Check if URL collection is marked as completed
        url_collection_completed = redis_client.get(f"{scraper_name.lower()}_url_collection_completed")
        
        # If URL collection is completed but there are still items in the queue
        if url_collection_completed and total_items > 0:
            if status not in ["stopping", "error"]:
                status = "processing_remaining"
                set_scraper_status(scraper_name, "processing_remaining", 
                                   message=f"URL collection completed, processing {total_items} remaining items")
        
        # If URL collection is completed and no items in queue
        elif url_collection_completed and total_items == 0:
            status = "completed"
            set_scraper_status(scraper_name, "completed", 
                               message="URL collection completed and all items processed")
        
        # If queues have items but status is idle, set to running
        elif total_items > 0 and status == "idle":
            status = "running"
            set_scraper_status(scraper_name, "running", 
                               message=f"Processing {total_items} items")
        
        return {
            "status": status,
            "message": get_scraper_status(scraper_name).get("message", ""),
            "stats": frontend_queue_stats,
            "url_collection_completed": bool(url_collection_completed),
            "queue_management_info": queue_management_info
        }
        
    except Exception as e:
        logger.exception(f"Error getting status for scraper {scraper_name}")
        return {
            "status": "error",
            "message": f"Error getting status: {str(e)}"
        }
    finally:
        db.close()


@router.get("/activity", response_model=List[Dict[str, Any]])
def get_scraper_activity(
    db: Session = Depends(get_db),
    limit: int = 10,
    current_user: User = Depends(get_current_user_from_clerk)
) -> Any:
    """Get recent scraper activity.
    Requires authentication.
    
    Args:
        db: Database session
        limit: Maximum number of records to return
        current_user: Current authenticated user
        
    Returns:
        List of recent scraper activity records
    """
    activities = db.query(ScraperActivity).order_by(ScraperActivity.start_time.desc()).limit(limit).all()
    
    return [
        {
            "id": activity.id,
            "scraper": activity.scraper_name,
            "action": activity.status,
            "details": activity.message,
            "timestamp": activity.start_time.isoformat() if activity.start_time else None
        }
        for activity in activities
    ]


@router.delete("/purge-database", response_model=schemas.ScraperRunResponse)
def purge_database(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
) -> Any:
    """
    Purge all listings and scraper activities from the database.
    Admin only.
    """
    try:
        # Delete all listings
        listings_count = crud.listing.remove_all(db=db)
        
        # Delete all scraper activities
        print(current_user)
        activities_count = db.query(ScraperActivity).delete()
        db.commit()
        
        return {
            "success": True,
            "message": f"Database purged successfully. Removed {listings_count} listings and {activities_count} activities."
        }
    except Exception as e:
        db.rollback()
        logger.error(f"Error purging database: {e}")
        raise HTTPException(status_code=500, detail=f"Error purging database: {str(e)}")


@router.get("/queue-stats", response_model=Dict[str, Any])
def get_queue_stats(
    current_user: User = Depends(get_current_user_from_clerk)
):
    """Get unified statistics about all LLM queues.
    Requires authentication."""
    try:
        # Get all LLM queue lengths (queues that feed into unified LLM processing)
        agenz_details_queue = redis_client.llen("agenz_details_queue")
        sarouty_processed_queue = redis_client.llen("sarouty_processed_queue")  # Sarouty uses processed queue
        avito_details_queue = redis_client.llen("avito_details_queue")
        
        # Total items awaiting LLM processing across all scrapers
        total_llm_queue = agenz_details_queue + sarouty_processed_queue + avito_details_queue
        
        return {
            "total_llm_queue": total_llm_queue,
            "agenz_details_queue": agenz_details_queue,
            "sarouty_processed_queue": sarouty_processed_queue,
            "avito_details_queue": avito_details_queue
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/llm-queue-stats", response_model=Dict[str, Any])
def get_llm_queue_stats(
    current_user: User = Depends(get_current_user_from_clerk)
):
    """Get detailed statistics about the unified LLM processing queue.
    Requires authentication."""
    try:
        # Get all queues that feed into unified LLM processing
        agenz_details_queue = redis_client.llen("agenz_details_queue")
        sarouty_processed_queue = redis_client.llen("sarouty_processed_queue")  # Sarouty uses processed queue
        avito_details_queue = redis_client.llen("avito_details_queue")
        
        # Check for empty queue markers and adjust sizes
        for queue_name, queue_size in [
            ("agenz_details_queue", agenz_details_queue),
            ("sarouty_processed_queue", sarouty_processed_queue), 
            ("avito_details_queue", avito_details_queue)
        ]:
            if queue_size > 0:
                first_item = redis_client.lindex(queue_name, 0)
                if first_item in [b'', b'__QUEUE_MARKER__']:
                    if queue_name == "agenz_details_queue":
                        agenz_details_queue -= 1
                    elif queue_name == "sarouty_processed_queue":
                        sarouty_processed_queue -= 1
                    elif queue_name == "avito_details_queue":
                        avito_details_queue -= 1
        
        # Total items awaiting LLM processing
        total_llm_queue = agenz_details_queue + sarouty_processed_queue + avito_details_queue
        
        return {
            "total_llm_queue": total_llm_queue,
            "breakdown": {
                "agenz": agenz_details_queue,
                "sarouty": sarouty_processed_queue,
                "avito": avito_details_queue
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/clear-queues", response_model=Dict[str, Any])
def clear_queues(
    current_user: User = Depends(get_current_admin_user)
):
    """Clear all the queues.
    Admin only."""
    try:
        redis_client.delete("agenz_url_queue")
        redis_client.delete("agenz_details_queue")
        redis_client.delete("agenz_processed_queue")
        
        return {
            "status": "success",
            "message": "Queues cleared successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/clear-llm-queues", response_model=Dict[str, Any])
def clear_llm_queues(
    current_user: User = Depends(get_current_admin_user)
):
    """Clear only the LLM queues (queues that feed into LLM processing).
    Admin only."""
    try:
        # Clear all LLM processing queues using the registry
        cleared_queues = []
        for queue_key, config in LLM_QUEUE_REGISTRY.items():
            if config["enabled"] and not config.get("is_legacy", False):
                queue_name = config["queue_name"]
                redis_client.delete(queue_name)
                cleared_queues.append(queue_name)
        
        return {
            "status": "success",
            "message": f"LLM queues cleared successfully. Cleared {len(cleared_queues)} queues: {', '.join(cleared_queues)}",
            "cleared_queues": cleared_queues
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{scraper_name}/reset-url-collection", response_model=Dict[str, Any])
def reset_url_collection(
    scraper_name: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
) -> Any:
    """Reset the URL collection status, allowing URL collection to start again.
    Admin only.
    
    Args:
        scraper_name: Name of the scraper to reset
        db: Database session
        current_user: Current authenticated admin user
        
    Returns:
        Status of the reset operation
    """
    # Check if scraper is valid using the registry
    if not is_valid_scraper(scraper_name):
        raise HTTPException(status_code=400, detail=f"Unsupported scraper: {scraper_name}")
    
    try:
        # Get scraper configuration from registry
        scraper_config = get_scraper_from_registry(scraper_name)
        scraper_class = scraper_config["scraper_class"]
        
        # Create a scraper instance to get the proper scraper name for Redis keys
        db_temp = SessionLocal()
        scraper = scraper_class(db_temp)
        
        # Use the scraper's internal name for Redis keys (handles legacy mappings)
        actual_scraper_name = scraper.scraper_name if hasattr(scraper, 'scraper_name') else scraper_name.lower()
        
        # Delete the URL collection completion flag
        redis_client.delete(f"{actual_scraper_name}_url_collection_completed")
        
        # Reset the last page to 1
        redis_client.set(f"{actual_scraper_name}_last_page", "1")
        
        db_temp.close()
        
        # Update the scraper status
        set_scraper_status(scraper_name, "idle", message="URL collection reset, ready to start again")
        
        return {
            "status": "success", 
            "message": f"URL collection status for '{scraper_name}' has been reset"
        }
        
    except Exception as e:
        logger.exception(f"Error resetting URL collection for {scraper_name}")
        return {"status": "error", "message": f"Error resetting URL collection: {str(e)}"}


@router.post("/debug-queue/{queue_name}", response_model=Dict[str, Any])
def debug_queue(
    queue_name: str,
    current_user: User = Depends(get_current_admin_user)
):
    """Debug and fix issues with a specific queue.
    Admin only."""
    try:
        if queue_name not in ["agenz_url_queue", "agenz_details_queue", "agenz_processed_queue"]:
            raise HTTPException(status_code=400, detail=f"Unknown queue: {queue_name}")
        
        # Get queue length
        queue_length = redis_client.llen(queue_name)
        
        # Get the first 10 items without removing them
        queue_items = redis_client.lrange(queue_name, 0, 9)
        decoded_items = [item.decode('utf-8') if item else "empty" for item in queue_items]
        
        # Check for empty items
        empty_items_count = sum(1 for item in queue_items if not item or item == b'' or item == b'__QUEUE_MARKER__')
        
        # Check if queue needs repair
        needs_repair = queue_length == 1 and empty_items_count == 1
        
        repair_result = None
        if needs_repair:
            # Remove the empty marker
            redis_client.lrem(queue_name, 0, b'')
            redis_client.lrem(queue_name, 0, b'__QUEUE_MARKER__')
            repair_result = "Removed empty queue marker"
        
        # Get queue length after repair
        new_queue_length = redis_client.llen(queue_name)
        
        return {
            "queue_name": queue_name,
            "original_length": queue_length,
            "empty_items_count": empty_items_count,
            "needs_repair": needs_repair,
            "repair_result": repair_result,
            "new_length": new_queue_length,
            "sample_items": decoded_items
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/verify-urls", response_model=Dict[str, Any])
def verify_urls_in_queue(
    current_user: User = Depends(get_current_admin_user)
):
    """Verify and repair the URL queue by checking for actual URLs.
    Admin only."""
    try:
        # Get queue length
        queue_length = redis_client.llen("agenz_url_queue")
        logger.info(f"URL queue length: {queue_length}")
        
        # Create a temporary list to hold valid URLs
        valid_urls = []
        empty_count = 0
        invalid_count = 0
        
        # Drain the queue and filter out empty or invalid items
        while redis_client.llen("agenz_url_queue") > 0:
            url = redis_client.lpop("agenz_url_queue")
            if not url or url == b'' or url == b'__QUEUE_MARKER__':
                empty_count += 1
                continue
                
            # Check if it's a valid URL
            try:
                decoded_url = url.decode('utf-8')
                if decoded_url and decoded_url.startswith(('http://', 'https://')):
                    valid_urls.append(decoded_url)
                else:
                    invalid_count += 1
            except:
                invalid_count += 1
        
        # Push all valid URLs back to the queue
        for url in valid_urls:
            redis_client.rpush("agenz_url_queue", url)
        
        # Get new queue length
        new_queue_length = redis_client.llen("agenz_url_queue")
        
        return {
            "original_length": queue_length,
            "empty_items_removed": empty_count,
            "invalid_items_removed": invalid_count,
            "valid_urls_count": len(valid_urls),
            "new_queue_length": new_queue_length,
            "sample_urls": valid_urls[:10] if valid_urls else []
        }
        
    except Exception as e:
        logger.error(f"Error verifying URL queue: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/dashboard-stats", response_model=Dict[str, Any])
def get_dashboard_stats(
    current_user: User = Depends(get_current_user_from_clerk)
):
    """Get dashboard statistics including unified LLM queue and individual scraper stats.
    Requires authentication."""
    try:
        # Get LLM queue stats using the registry
        queue_stats, total_llm_queue = get_registered_llm_queue_stats()
        
        # Get LLM worker status
        llm_worker_status = "idle"
        llm_status_message = "No items in queue"
        rate_limit_info = None
        
        # Check if rate limited
        if is_rate_limited():
            llm_worker_status = "rate_limited"
            # Get rate limit details using the correct key from llm_service.py
            rate_limited_until = redis_client.get("llm_rate_limited_until")  # Correct key name
            if rate_limited_until:
                try:
                    until_timestamp = float(rate_limited_until.decode('utf-8'))
                    remaining_seconds = max(0, int(until_timestamp - time.time()))
                    rate_limit_info = {
                        "remaining_seconds": remaining_seconds,
                        "until_timestamp": until_timestamp
                    }
                    llm_status_message = f"Rate limited for {remaining_seconds} more seconds"
                except (ValueError, TypeError):
                    llm_status_message = "Rate limited (unknown duration)"
            else:
                llm_status_message = "Rate limited"
        elif total_llm_queue > 0:
            llm_worker_status = "working"
            llm_status_message = f"Processing {total_llm_queue} items"
        else:
            llm_worker_status = "idle"
            llm_status_message = "Queue empty"
        
        # Get processing rate (items processed in last hour)
        # We'll track this using a Redis key that stores recent processing timestamps
        processing_rate_key = "llm_processing_rate"
        current_time = int(time.time())
        hour_ago = current_time - 3600  # 1 hour ago
        
        # Get recent processing timestamps and count those within the last hour
        recent_timestamps = redis_client.lrange(processing_rate_key, 0, -1)
        recent_count = 0
        for timestamp_bytes in recent_timestamps:
            try:
                timestamp = int(timestamp_bytes.decode('utf-8'))
                if timestamp >= hour_ago:
                    recent_count += 1
            except (ValueError, TypeError):
                continue
        
        # Calculate rate per hour and per minute
        items_per_hour = recent_count
        items_per_minute = round(recent_count / 60, 2) if recent_count > 0 else 0
        
        return {
            "unified_llm_queue": {
                "total": total_llm_queue,
                "breakdown": queue_stats
            },
            "llm_worker": {
                "status": llm_worker_status,
                "message": llm_status_message,
                "rate_limit_info": rate_limit_info
            },
            "processing_rate": {
                "items_per_hour": items_per_hour,
                "items_per_minute": items_per_minute,
                "last_hour_count": recent_count
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


def record_llm_processing():
    """Record an LLM processing event for rate tracking."""
    try:
        processing_rate_key = "llm_processing_rate"
        current_time = int(time.time())
        
        # Add current timestamp to the list
        redis_client.lpush(processing_rate_key, str(current_time))
        
        # Keep only the last 24 hours of data (trim the list)
        # Assuming max 1 item per second, 24 hours = 86400 items max
        redis_client.ltrim(processing_rate_key, 0, 86399)
        
        # Set expiration for the key (25 hours to be safe)
        redis_client.expire(processing_rate_key, 90000)
        
    except Exception as e:
        logger.error(f"Error recording LLM processing event: {str(e)}")


@router.post("/record-processing", response_model=Dict[str, Any])
def record_processing_event(
    current_user: User = Depends(get_current_admin_user)
):
    """Record an LLM processing event (called by workers when they process an item).
    Admin only."""
    try:
        record_llm_processing()
        return {"status": "success", "message": "Processing event recorded"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test-rate-limit", response_model=Dict[str, Any])
def test_rate_limit(
    duration: int = 60,
    current_user: User = Depends(get_current_admin_user)
):
    """Manually set rate limiting for testing purposes.
    Admin only."""
    try:
        from app.services.scrapers.llm_service import set_rate_limited
        set_rate_limited(duration)
        return {"status": "success", "message": f"Rate limit set for {duration} seconds"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Create a scraper registry that holds all available scrapers
SCRAPER_REGISTRY = {
    "agenz": {
        "name": "agenz",
        "display_name": "Agenz",
        "description": "",
        "source_website": "agenz.ma",
        "scraper_class": AgenzScraper,
        "supported_operations": ["run", "stop", "status", "reset_url_collection"],
        "property_types": ["sale", "rent"],
        "enabled": True
    },
    "sarouty": {
        "name": "sarouty",
        "display_name": "Sarouty",
        "description": "",
        "source_website": "sarouty.ma",
        "scraper_class": SaroutyScraper,
        "supported_operations": ["run", "stop", "status", "reset_url_collection"],
        "property_types": ["sale", "rent"],
        "enabled": True
    },
    "avito_sale": {
        "name": "avito_sale",
        "display_name": "Avito Sale",
        "description": "",
        "source_website": "avito.ma",
        "scraper_class": AvitoSaleScraper,
        "supported_operations": ["run", "stop", "status", "reset_url_collection"],
        "property_types": ["sale"],
        "enabled": True
    },
    "avito_rent": {
        "name": "avito_rent",
        "display_name": "Avito Rent",
        "description": "",
        "source_website": "avito.ma",
        "scraper_class": AvitoRentScraper,
        "supported_operations": ["run", "stop", "status", "reset_url_collection"],
        "property_types": ["rent"],
        "enabled": True
    },
    # Legacy support for "avito" (maps to avito_sale)
    "avito": {
        "name": "avito",
        "display_name": "Avito Sale",
        "description": "",
        "source_website": "avito.ma",
        "scraper_class": AvitoSaleScraper,
        "supported_operations": ["run", "stop", "status", "reset_url_collection"],
        "property_types": ["sale"],
        "enabled": True,
        "is_legacy": True
    }
}

# Create an LLM queue registry that holds all LLM processing queues
LLM_QUEUE_REGISTRY = {
    "agenz": {
        "scraper_name": "agenz",
        "display_name": "Agenz",
        "queue_name": "agenz_details_queue",
        "queue_type": "details",
        "description": "Properties waiting for LLM processing from Agenz",
        "color": "text-blue-600",
        "enabled": True
    },
    "sarouty": {
        "scraper_name": "sarouty", 
        "display_name": "Sarouty",
        "queue_name": "sarouty_processed_queue",
        "queue_type": "processed", 
        "description": "Properties waiting for LLM processing from Sarouty",
        "color": "text-purple-600",
        "enabled": True
    },
    "avito_sale": {
        "scraper_name": "avito_sale",
        "display_name": "Avito Sale", 
        "queue_name": "avito_sale_details_queue",
        "queue_type": "details",
        "description": "Sale properties waiting for LLM processing from Avito",
        "color": "text-orange-600",
        "enabled": True
    },
    "avito_rent": {
        "scraper_name": "avito_rent",
        "display_name": "Avito Rent",
        "queue_name": "avito_rent_details_queue", 
        "queue_type": "details",
        "description": "Rental properties waiting for LLM processing from Avito",
        "color": "text-green-600",
        "enabled": True
    },
    # Legacy mapping for "avito" queue stats (maps to combined avito queues)
    "avito": {
        "scraper_name": "avito",
        "display_name": "Avito",
        "queue_name": "avito_details_queue",  # Legacy queue name
        "queue_type": "details",
        "description": "Legacy Avito queue (combines sale and rent)",
        "color": "text-orange-600", 
        "enabled": True,
        "is_legacy": True,
        "combines": ["avito_sale", "avito_rent"]  # Combines these queues for backward compatibility
    }
}

@router.get("/registered", response_model=Dict[str, Any])
def get_registered_scrapers(
    current_user: User = Depends(get_current_user_from_clerk)
) -> Any:
    """Get all registered scrapers with their metadata.
    Returns scraper configuration that the frontend can use to dynamically generate controls.
    """
    try:
        # Return only enabled scrapers, excluding legacy ones by default
        enabled_scrapers = {
            name: {
                "name": config["name"],
                "display_name": config["display_name"],
                "description": config["description"],
                "source_website": config["source_website"],
                "supported_operations": config["supported_operations"],
                "property_types": config["property_types"],
                "enabled": config["enabled"],
                "is_legacy": config.get("is_legacy", False)
            }
            for name, config in SCRAPER_REGISTRY.items()
            if config["enabled"] and not config.get("is_legacy", False)
        }
        
        return {
            "scrapers": enabled_scrapers,
            "total_count": len(enabled_scrapers),
            "supported_operations": [
                "run",
                "stop", 
                "status",
                "reset_url_collection"
            ]
        }
    except Exception as e:
        logger.error(f"Error getting registered scrapers: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/registered/all", response_model=Dict[str, Any])
def get_all_registered_scrapers(
    current_user: User = Depends(get_current_admin_user)
) -> Any:
    """Get all registered scrapers including legacy ones.
    Admin only endpoint that returns complete scraper registry.
    """
    try:
        return {
            "scrapers": SCRAPER_REGISTRY,
            "total_count": len(SCRAPER_REGISTRY),
            "enabled_count": len([s for s in SCRAPER_REGISTRY.values() if s["enabled"]]),
            "legacy_count": len([s for s in SCRAPER_REGISTRY.values() if s.get("is_legacy", False)])
        }
    except Exception as e:
        logger.error(f"Error getting all registered scrapers: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def get_scraper_from_registry(scraper_name: str):
    """Get scraper configuration from registry.
    
    Args:
        scraper_name: Name of the scraper
        
    Returns:
        Scraper configuration dict or None if not found
    """
    return SCRAPER_REGISTRY.get(scraper_name.lower())

def is_valid_scraper(scraper_name: str) -> bool:
    """Check if a scraper name is valid and enabled.
    
    Args:
        scraper_name: Name of the scraper to check
        
    Returns:
        bool: True if scraper is valid and enabled, False otherwise
    """
    config = get_scraper_from_registry(scraper_name)
    return config is not None and config["enabled"]

@router.get("/llm-queues/registered", response_model=Dict[str, Any])
def get_registered_llm_queues(
    current_user: User = Depends(get_current_user_from_clerk)
) -> Any:
    """Get all registered LLM queues with their metadata.
    Returns LLM queue configuration that the frontend can use to dynamically generate stats.
    """
    try:
        # Return only enabled LLM queues, excluding legacy ones by default
        enabled_queues = {
            name: {
                "scraper_name": config["scraper_name"],
                "display_name": config["display_name"],
                "queue_name": config["queue_name"],
                "queue_type": config["queue_type"],
                "description": config["description"],
                "color": config["color"],
                "enabled": config["enabled"],
                "is_legacy": config.get("is_legacy", False),
                "combines": config.get("combines", [])
            }
            for name, config in LLM_QUEUE_REGISTRY.items()
            if config["enabled"] and not config.get("is_legacy", False)
        }
        
        return {
            "llm_queues": enabled_queues,
            "total_count": len(enabled_queues),
            "queue_types": list(set(config["queue_type"] for config in enabled_queues.values()))
        }
    except Exception as e:
        logger.error(f"Error getting registered LLM queues: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/llm-queues/registered/all", response_model=Dict[str, Any])
def get_all_registered_llm_queues(
    current_user: User = Depends(get_current_admin_user)
) -> Any:
    """Get all registered LLM queues including legacy ones.
    Admin only endpoint that returns complete LLM queue registry.
    """
    try:
        return {
            "llm_queues": LLM_QUEUE_REGISTRY,
            "total_count": len(LLM_QUEUE_REGISTRY),
            "enabled_count": len([q for q in LLM_QUEUE_REGISTRY.values() if q["enabled"]]),
            "legacy_count": len([q for q in LLM_QUEUE_REGISTRY.values() if q.get("is_legacy", False)])
        }
    except Exception as e:
        logger.error(f"Error getting all registered LLM queues: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/llm-queue/status", response_model=Dict[str, Any])
def get_llm_queue_status_endpoint(
    current_user: User = Depends(get_current_user_from_clerk)
) -> Any:
    """Get the current LLM queue status.
    Requires authentication.
    """
    try:
        status = get_llm_queue_status()
        return {
            "status": status,
            "is_running": status == "running",
            "is_stopped": status == "stopped",
            "is_paused": status == "paused"
        }
    except Exception as e:
        logger.error(f"Error getting LLM queue status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/llm-queue/start", response_model=Dict[str, Any])
def start_llm_queue(
    current_user: User = Depends(get_current_admin_user)
) -> Any:
    """Start the LLM queue processing.
    Admin only.
    """
    try:
        current_status = get_llm_queue_status()
        if current_status == "running":
            return {
                "status": "already_running",
                "message": "LLM queue is already running"
            }
        
        send_llm_queue_control_command("start")
        set_llm_queue_status("running")
        
        return {
            "status": "started",
            "message": "LLM queue started successfully"
        }
    except Exception as e:
        logger.error(f"Error starting LLM queue: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/llm-queue/stop", response_model=Dict[str, Any])
def stop_llm_queue(
    current_user: User = Depends(get_current_admin_user)
) -> Any:
    """Stop the LLM queue processing.
    Admin only.
    """
    try:
        current_status = get_llm_queue_status()
        if current_status == "stopped":
            return {
                "status": "already_stopped",
                "message": "LLM queue is already stopped"
            }
        
        send_llm_queue_control_command("stop")
        set_llm_queue_status("stopped")
        
        return {
            "status": "stopped",
            "message": "LLM queue stopped successfully"
        }
    except Exception as e:
        logger.error(f"Error stopping LLM queue: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/llm-queue/pause", response_model=Dict[str, Any])
def pause_llm_queue(
    current_user: User = Depends(get_current_admin_user)
) -> Any:
    """Pause the LLM queue processing.
    Admin only.
    """
    try:
        current_status = get_llm_queue_status()
        if current_status == "paused":
            return {
                "status": "already_paused",
                "message": "LLM queue is already paused"
            }
        
        send_llm_queue_control_command("pause")
        set_llm_queue_status("paused")
        
        return {
            "status": "paused",
            "message": "LLM queue paused successfully"
        }
    except Exception as e:
        logger.error(f"Error pausing LLM queue: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/llm-queue/resume", response_model=Dict[str, Any])
def resume_llm_queue(
    current_user: User = Depends(get_current_admin_user)
) -> Any:
    """Resume the LLM queue processing.
    Admin only.
    """
    try:
        current_status = get_llm_queue_status()
        if current_status == "running":
            return {
                "status": "already_running",
                "message": "LLM queue is already running"
            }
        
        send_llm_queue_control_command("resume")
        set_llm_queue_status("running")
        
        return {
            "status": "resumed",
            "message": "LLM queue resumed successfully"
        }
    except Exception as e:
        logger.error(f"Error resuming LLM queue: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/queue-management/status", response_model=Dict[str, Any])
def get_queue_management_status(
    current_user: User = Depends(get_current_user_from_clerk)
) -> Any:
    """Get the current queue management status.
    Shows automatic pause/resume functionality status and statistics.
    """
    try:
        from app.services.scrapers.queue_manager import get_queue_management_status
        return get_queue_management_status()
    except Exception as e:
        logger.error(f"Error getting queue management status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/queue-management/check", response_model=Dict[str, Any])
def trigger_queue_management_check(
    current_user: User = Depends(get_current_admin_user)
) -> Any:
    """Manually trigger a queue management check.
    Admin only.
    """
    try:
        from app.services.scrapers.queue_manager import queue_limit_manager
        status = queue_limit_manager.check_and_manage_queue_limits()
        return {
            "status": "success",
            "message": "Queue management check completed",
            "result": status
        }
    except Exception as e:
        logger.error(f"Error triggering queue management check: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def get_llm_queue_from_registry(queue_name: str):
    """Get LLM queue configuration from registry.
    
    Args:
        queue_name: Name of the LLM queue
        
    Returns:
        LLM queue configuration dict or None if not found
    """
    return LLM_QUEUE_REGISTRY.get(queue_name.lower())

def get_registered_llm_queue_stats():
    """Get current queue sizes for all registered LLM queues.
    
    Returns:
        Dict with queue sizes for each registered queue
    """
    queue_stats = {}
    total_items = 0
    
    for queue_key, config in LLM_QUEUE_REGISTRY.items():
        if not config["enabled"] or config.get("is_legacy", False):
            continue
            
        queue_name = config["queue_name"]
        queue_size = redis_client.llen(queue_name)
        
        # Check for empty queue markers and adjust size
        if queue_size > 0:
            first_item = redis_client.lindex(queue_name, 0)
            if first_item in [b'', b'__QUEUE_MARKER__']:
                queue_size -= 1
                
        queue_stats[queue_key] = queue_size
        total_items += queue_size
    
    # Handle legacy "avito" queue for backward compatibility
    avito_config = LLM_QUEUE_REGISTRY.get("avito")
    if avito_config and avito_config.get("combines"):
        # Combine the queues it represents
        combined_size = sum(
            queue_stats.get(combined_queue, 0) 
            for combined_queue in avito_config["combines"]
        )
        queue_stats["avito"] = combined_size
        
    return queue_stats, total_items 

@router.post("/queue-management/auto-resume", response_model=Dict[str, Any])
def trigger_auto_resume_all_scrapers(
    current_user: User = Depends(get_current_admin_user)
) -> Any:
    """Manually trigger auto-resume for all eligible scrapers.
    Admin only.
    """
    try:
        from app.services.scrapers.queue_manager import queue_limit_manager
        result = queue_limit_manager.trigger_auto_resume_all()
        return {
            "status": "success",
            "message": "Auto-resume triggered for all eligible scrapers",
            "result": result
        }
    except Exception as e:
        logger.error(f"Error triggering auto-resume: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/queue-management/detailed-status", response_model=Dict[str, Any])
def get_detailed_queue_management_status(
    current_user: User = Depends(get_current_user_from_clerk)
) -> Any:
    """Get detailed queue management status including auto-resume information.
    """
    try:
        from app.services.scrapers.queue_manager import queue_limit_manager, get_queue_management_status
        
        # Get basic status
        status = get_queue_management_status()
        
        # Add detailed scraper information
        scraper_details = {}
        for scraper_name in queue_limit_manager.active_scrapers:
            is_running = queue_limit_manager.is_scraper_running(scraper_name)
            is_paused = scraper_name in status.get("paused_scrapers", [])
            is_auto_resume = scraper_name in status.get("auto_resume_scrapers", [])
            
            scraper_details[scraper_name] = {
                "is_running": is_running,
                "is_paused_for_queue": is_paused,
                "is_in_auto_resume": is_auto_resume,
                "should_be_resumed": is_paused or is_auto_resume
            }
        
        return {
            **status,
            "scraper_details": scraper_details
        }
    except Exception as e:
        logger.error(f"Error getting detailed queue management status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 