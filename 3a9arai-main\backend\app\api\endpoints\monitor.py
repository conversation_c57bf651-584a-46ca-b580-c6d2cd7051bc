"""Endpoints for monitoring Celery workers and tasks."""
import logging
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from datetime import datetime, timedelta

from app.api.deps import get_db
from app.models.scraper_activity import ScraperActivity
from app.worker import celery

# Replace the deprecated SCRAPERS with our new supported scrapers
SUPPORTED_SCRAPERS = {
    "agenz": "Agenz Real Estate"
}

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/workers", response_model=Dict[str, Any])
def get_worker_status(db: Session = Depends(get_db)):
    """Get Celery worker status and statistics."""
    try:
        # Get active workers and their stats
        worker_stats = {}
        inspection = celery.control.inspect()
        
        # Get active workers
        active_workers = inspection.active() or {}
        worker_stats["active_workers"] = len(active_workers)
        worker_stats["active_tasks"] = []
        
        for worker, tasks in active_workers.items():
            for task in tasks:
                worker_stats["active_tasks"].append({
                    "worker": worker,
                    "task_id": task.get("id"),
                    "task_name": task.get("name"),
                    "args": task.get("args"),
                    "started_at": task.get("time_start")
                })
        
        # Get reserved tasks
        reserved = inspection.reserved() or {}
        worker_stats["reserved_tasks"] = sum(len(tasks) for tasks in reserved.values())
        
        # Get registered tasks
        registered = inspection.registered() or {}
        worker_stats["registered_task_types"] = {}
        for worker, tasks in registered.items():
            for task in tasks:
                if task not in worker_stats["registered_task_types"]:
                    worker_stats["registered_task_types"][task] = 0
                worker_stats["registered_task_types"][task] += 1
        
        # Get queue statistics
        queues = inspection.scheduled() or {}
        scheduled_tasks = sum(len(tasks) for tasks in queues.values())
        worker_stats["scheduled_tasks"] = scheduled_tasks
        
        return worker_stats
    except Exception as e:
        logger.error(f"Error fetching worker status: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching worker status: {str(e)}")

@router.get("/activities", response_model=List[Dict[str, Any]])
def get_scraper_activities(
    limit: int = 10, 
    offset: int = 0,
    scraper_name: Optional[str] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get recent scraper activities."""
    try:
        query = db.query(ScraperActivity)
        
        # Apply filters
        if scraper_name:
            query = query.filter(ScraperActivity.scraper_name == scraper_name)
        if status:
            query = query.filter(ScraperActivity.status == status)
        
        # Order by most recent first
        query = query.order_by(desc(ScraperActivity.start_time))
        
        # Apply pagination
        total = query.count()
        activities = query.offset(offset).limit(limit).all()
        
        # Format and return activities
        formatted_activities = []
        for activity in activities:
            elapsed = None
            if activity.start_time:
                if activity.end_time:
                    elapsed = (activity.end_time - activity.start_time).total_seconds()
                else:
                    elapsed = (datetime.utcnow() - activity.start_time).total_seconds()
            
            formatted_activities.append({
                "id": activity.id,
                "scraper_name": activity.scraper_name,
                "status": activity.status,
                "message": activity.message,
                "listings_added": activity.listings_added,
                "start_time": activity.start_time.isoformat() if activity.start_time else None,
                "end_time": activity.end_time.isoformat() if activity.end_time else None,
                "elapsed_seconds": elapsed
            })
        
        return formatted_activities
    except Exception as e:
        logger.error(f"Error fetching scraper activities: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching scraper activities: {str(e)}")

@router.get("/stats", response_model=Dict[str, Any])
def get_scraper_stats(days: int = 7, db: Session = Depends(get_db)):
    """Get statistics about scraper performance."""
    try:
        # Calculate the date threshold
        threshold_date = datetime.utcnow() - timedelta(days=days)
        
        # Get stats for each scraper
        scraper_stats = {}
        
        for scraper_name in SUPPORTED_SCRAPERS.keys():
            # Get total activities
            total_activities = db.query(func.count(ScraperActivity.id))\
                .filter(ScraperActivity.scraper_name == scraper_name)\
                .scalar() or 0
            
            # Get recent activities
            recent_activities = db.query(func.count(ScraperActivity.id))\
                .filter(
                    ScraperActivity.scraper_name == scraper_name,
                    ScraperActivity.start_time >= threshold_date
                )\
                .scalar() or 0
            
            # Get listings added
            listings_added = db.query(func.sum(ScraperActivity.listings_added))\
                .filter(
                    ScraperActivity.scraper_name == scraper_name,
                    ScraperActivity.start_time >= threshold_date
                )\
                .scalar() or 0
            
            # Get success rate
            success_count = db.query(func.count(ScraperActivity.id))\
                .filter(
                    ScraperActivity.scraper_name == scraper_name,
                    ScraperActivity.status == "success",
                    ScraperActivity.start_time >= threshold_date
                )\
                .scalar() or 0
            
            error_count = db.query(func.count(ScraperActivity.id))\
                .filter(
                    ScraperActivity.scraper_name == scraper_name,
                    ScraperActivity.status == "failed",
                    ScraperActivity.start_time >= threshold_date
                )\
                .scalar() or 0
            
            success_rate = 0
            if recent_activities > 0:
                success_rate = (success_count / recent_activities) * 100
            
            # Get average execution time
            avg_execution_time = db.query(
                func.avg(
                    func.extract('epoch', ScraperActivity.end_time) - 
                    func.extract('epoch', ScraperActivity.start_time)
                )
            )\
                .filter(
                    ScraperActivity.scraper_name == scraper_name,
                    ScraperActivity.start_time >= threshold_date,
                    ScraperActivity.end_time.isnot(None)
                )\
                .scalar() or 0
            
            # Compile stats
            scraper_stats[scraper_name] = {
                "total_activities": total_activities,
                "recent_activities": recent_activities,
                "listings_added": listings_added,
                "success_count": success_count,
                "error_count": error_count,
                "success_rate": success_rate,
                "avg_execution_time": avg_execution_time
            }
        
        return {
            "days": days,
            "scrapers": scraper_stats
        }
    except Exception as e:
        logger.error(f"Error fetching scraper stats: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching scraper stats: {str(e)}") 