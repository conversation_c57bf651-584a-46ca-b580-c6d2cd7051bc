from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps
from app.crud.crud_listing_view import listing_view

router = APIRouter()


@router.post("/track/{listing_id}", response_model=schemas.ListingViewResponse)
def track_listing_view(
    *,
    db: Session = Depends(deps.get_db),
    listing_id: int,
    request: Request,
) -> Any:
    """
    Track a listing view (works for both authenticated and anonymous users).
    Uses IP address and user agent for uniqueness tracking.
    """
    # Check if listing exists
    listing = crud.listing.get(db=db, id=listing_id)
    if not listing:
        raise HTTPException(status_code=404, detail="Listing not found")
    
    # Get client IP address
    client_ip = request.client.host
    if hasattr(request, 'headers'):
        # Check for forwarded headers (useful for proxies)
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            client_ip = forwarded_for.split(',')[0].strip()
    
    # Get user agent
    user_agent = request.headers.get('User-Agent', '')
    
    # Track the view
    view = listing_view.track_view(
        db=db,
        listing_id=listing_id,
        ip_address=client_ip,
        user_agent=user_agent
    )
    
    return view


@router.get("/popular", response_model=List[schemas.Listing])
def get_popular_listings(
    db: Session = Depends(deps.get_db),
    hours: int = 24,
    limit: int = 10,
) -> Any:
    """
    Get the most popular listings based on view counts in the specified time period.
    """
    listings = listing_view.get_popular_listings(db=db, hours=hours, limit=limit)
    
    # Convert SQLAlchemy models to Pydantic schemas
    return [
        schemas.Listing.model_validate(deps.prepare_listing_for_response(listing)) 
        for listing in listings
    ]


@router.get("/count/{listing_id}")
def get_listing_view_count(
    *,
    db: Session = Depends(deps.get_db),
    listing_id: int,
    hours: int = 24,
) -> Any:
    """
    Get the view count for a specific listing in the specified time period.
    """
    # Check if listing exists
    listing = crud.listing.get(db=db, id=listing_id)
    if not listing:
        raise HTTPException(status_code=404, detail="Listing not found")
    
    count = listing_view.get_view_count(db=db, listing_id=listing_id, hours=hours)
    
    return {"listing_id": listing_id, "view_count": count, "hours": hours}


@router.get("/stats")
def get_view_stats(
    db: Session = Depends(deps.get_db),
    hours: int = 24,
    limit: int = 10,
) -> Any:
    """
    Get view statistics: most viewed listings with their counts.
    """
    stats = listing_view.get_most_viewed_listings(db=db, hours=hours, limit=limit)
    
    return {
        "most_viewed": [
            {"listing_id": listing_id, "view_count": view_count}
            for listing_id, view_count in stats
        ],
        "hours": hours,
        "total_listings": len(stats)
    } 