"""add_transaction_type_to_listings

Revision ID: ac9d10dd3b26
Revises: 041761bd06df
Create Date: 2025-06-18 15:07:07.420908

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ac9d10dd3b26'
down_revision: Union[str, None] = '041761bd06df'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add transaction_type column with default value 'sell'
    op.add_column('listings', sa.Column('transaction_type', sa.String(50), nullable=False, server_default='sell'))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove the transaction_type column
    op.drop_column('listings', 'transaction_type')
