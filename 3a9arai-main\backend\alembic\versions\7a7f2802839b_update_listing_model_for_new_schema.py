"""update_listing_model_for_new_schema

Revision ID: 7a7f2802839b
Revises: 8dede3a84054
Create Date: 2025-06-03 14:07:22.571123

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '7a7f2802839b'
down_revision: Union[str, None] = '8dede3a84054'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('listings', sa.Column('image_url', sa.String(length=500), nullable=True))
    op.add_column('listings', sa.Column('source_website', sa.String(length=50), nullable=True))
    op.add_column('listings', sa.Column('size', sa.Float(), nullable=True))
    op.add_column('listings', sa.Column('rooms', sa.Integer(), nullable=True))
    op.add_column('listings', sa.Column('bathrooms', sa.Integer(), nullable=True))
    op.alter_column('listings', 'title',
               existing_type=sa.VARCHAR(length=200),
               type_=sa.String(length=500),
               existing_nullable=False)
    op.alter_column('listings', 'price',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=False)
    op.alter_column('listings', 'size_sqm',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=False)
    op.alter_column('listings', 'note_about_size_sqm',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=200),
               nullable=False)
    op.alter_column('listings', 'city',
               existing_type=sa.VARCHAR(length=100),
               nullable=False)
    op.alter_column('listings', 'property_type',
               existing_type=sa.VARCHAR(length=50),
               nullable=False)
    op.alter_column('listings', 'property_category',
               existing_type=sa.VARCHAR(length=50),
               nullable=False)
    op.alter_column('listings', 'description_html',
               existing_type=sa.TEXT(),
               nullable=False)
    op.alter_column('listings', 'contact_name',
               existing_type=sa.VARCHAR(length=100),
               nullable=False)
    op.alter_column('listings', 'contact_phone',
               existing_type=sa.VARCHAR(length=100),
               nullable=False)
    op.alter_column('listings', 'images',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=False)
    op.alter_column('listings', 'scraper',
               existing_type=sa.VARCHAR(length=50),
               nullable=False)
    op.drop_column('listings', 'website_source')
    op.drop_column('listings', 'description')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('listings', sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('website_source', sa.VARCHAR(length=50), autoincrement=False, nullable=True))
    op.alter_column('listings', 'scraper',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
    op.alter_column('listings', 'images',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=True)
    op.alter_column('listings', 'contact_phone',
               existing_type=sa.VARCHAR(length=100),
               nullable=True)
    op.alter_column('listings', 'contact_name',
               existing_type=sa.VARCHAR(length=100),
               nullable=True)
    op.alter_column('listings', 'description_html',
               existing_type=sa.TEXT(),
               nullable=True)
    op.alter_column('listings', 'property_category',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
    op.alter_column('listings', 'property_type',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
    op.alter_column('listings', 'city',
               existing_type=sa.VARCHAR(length=100),
               nullable=True)
    op.alter_column('listings', 'note_about_size_sqm',
               existing_type=sa.String(length=200),
               type_=sa.VARCHAR(length=100),
               nullable=True)
    op.alter_column('listings', 'size_sqm',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=True)
    op.alter_column('listings', 'price',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=True)
    op.alter_column('listings', 'title',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=200),
               existing_nullable=False)
    op.drop_column('listings', 'bathrooms')
    op.drop_column('listings', 'rooms')
    op.drop_column('listings', 'size')
    op.drop_column('listings', 'source_website')
    op.drop_column('listings', 'image_url')
    # ### end Alembic commands ###
