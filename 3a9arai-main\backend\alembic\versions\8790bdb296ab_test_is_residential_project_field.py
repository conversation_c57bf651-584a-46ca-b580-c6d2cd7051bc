"""test_is_residential_project_field

Revision ID: 8790bdb296ab
Revises: 6c25871c0233
Create Date: 2025-04-23 11:17:05.763643

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8790bdb296ab'
down_revision: Union[str, None] = '6c25871c0233'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('listings', sa.Column('is_part_of_project', sa.<PERSON>(), nullable=True))
    op.drop_index('ix_listings_is_residential_project', table_name='listings')
    op.drop_column('listings', 'bedrooms')
    op.drop_column('listings', 'location')
    op.drop_column('scraper_activity', 'error_details')
    op.drop_column('scraper_activity', 'progress_total')
    op.drop_column('scraper_activity', 'progress_percent')
    op.drop_column('scraper_activity', 'progress_current')
    op.drop_column('scraper_activity', 'runtime_seconds')
    op.drop_column('scraper_activity', 'params')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('scraper_activity', sa.Column('params', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('scraper_activity', sa.Column('runtime_seconds', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('scraper_activity', sa.Column('progress_current', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('scraper_activity', sa.Column('progress_percent', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True))
    op.add_column('scraper_activity', sa.Column('progress_total', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('scraper_activity', sa.Column('error_details', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('location', sa.VARCHAR(length=200), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('bedrooms', sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_index('ix_listings_is_residential_project', 'listings', ['is_residential_project'], unique=False)
    op.drop_column('listings', 'is_part_of_project')
    # ### end Alembic commands ###
