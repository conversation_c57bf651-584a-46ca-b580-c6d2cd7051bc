"""empty message

Revision ID: 7c1eab1a42c4
Revises: 1535074216ff
Create Date: 2025-06-02 17:36:32.873162

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7c1eab1a42c4'
down_revision: Union[str, None] = '1535074216ff'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('listings', sa.Column('description_html', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('listings', 'description_html')
    # ### end Alembic commands ###
