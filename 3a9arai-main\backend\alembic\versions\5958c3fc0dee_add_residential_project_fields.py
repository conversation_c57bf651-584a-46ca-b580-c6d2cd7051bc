"""Add residential project fields

Revision ID: 5958c3fc0dee
Revises: 9a144ba4dc17
Create Date: 2025-03-10 18:15:18.885460

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5958c3fc0dee'
down_revision: Union[str, None] = '9a144ba4dc17'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('listings', sa.Column('is_residential_project', sa.<PERSON>(), nullable=True))
    op.add_column('listings', sa.Column('residential_project_name', sa.String(length=200), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('listings', 'residential_project_name')
    op.drop_column('listings', 'is_residential_project')
    # ### end Alembic commands ###
