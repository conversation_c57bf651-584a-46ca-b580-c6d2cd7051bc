"""add_user_id_to_listings_table_fix

Revision ID: 041761bd06df
Revises: 042a6a47b407
Create Date: 2025-06-18 14:33:57.682275

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '041761bd06df'
down_revision: Union[str, None] = '042a6a47b407'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('listings', sa.Column('user_id', sa.Integer(), nullable=True))
    op.create_index(op.f('ix_listings_user_id'), 'listings', ['user_id'], unique=False)
    op.create_foreign_key(None, 'listings', 'users', ['user_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'listings', type_='foreignkey')
    op.drop_index(op.f('ix_listings_user_id'), table_name='listings')
    op.drop_column('listings', 'user_id')
    # ### end Alembic commands ###
