"""add_approval_system_to_listings

Revision ID: f8301f9357b9
Revises: ac9d10dd3b26
Create Date: 2025-06-25 10:07:05.222205

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f8301f9357b9'
down_revision: Union[str, None] = 'ac9d10dd3b26'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create the enum type first (only if it doesn't exist)
    approval_status_enum = sa.Enum('pending', 'approved', 'rejected', name='approvalstatus')
    approval_status_enum.create(op.get_bind(), checkfirst=True)
    
    # Add columns as nullable first
    op.add_column('listings', sa.Column('approval_status', approval_status_enum, nullable=True))
    op.add_column('listings', sa.Column('is_user_generated', sa.<PERSON>(), nullable=True))
    op.add_column('listings', sa.Column('approved_by', sa.Integer(), nullable=True))
    op.add_column('listings', sa.Column('approval_date', sa.DateTime(), nullable=True))
    op.add_column('listings', sa.Column('rejection_reason', sa.Text(), nullable=True))
    
    # Set default values for existing records
    # All existing listings are assumed to be scraper-generated and auto-approved
    op.execute("""
        UPDATE listings 
        SET 
            approval_status = 'approved',
            is_user_generated = CASE 
                WHEN user_id IS NOT NULL THEN true 
                ELSE false 
            END
        WHERE approval_status IS NULL OR is_user_generated IS NULL
    """)
    
    # Now make the required columns NOT NULL
    op.alter_column('listings', 'approval_status', nullable=False)
    op.alter_column('listings', 'is_user_generated', nullable=False)
    
    # Create indexes and foreign key
    op.create_index(op.f('ix_listings_approval_status'), 'listings', ['approval_status'], unique=False)
    op.create_index(op.f('ix_listings_is_user_generated'), 'listings', ['is_user_generated'], unique=False)
    op.create_foreign_key(None, 'listings', 'users', ['approved_by'], ['id'])


def downgrade() -> None:
    """Downgrade schema."""
    # Drop foreign key and indexes
    op.drop_constraint(None, 'listings', type_='foreignkey')
    op.drop_index(op.f('ix_listings_is_user_generated'), table_name='listings')
    op.drop_index(op.f('ix_listings_approval_status'), table_name='listings')
    
    # Drop columns
    op.drop_column('listings', 'rejection_reason')
    op.drop_column('listings', 'approval_date')
    op.drop_column('listings', 'approved_by')
    op.drop_column('listings', 'is_user_generated')
    op.drop_column('listings', 'approval_status')
    
    # Drop the enum type
    sa.Enum(name='approvalstatus').drop(op.get_bind())
