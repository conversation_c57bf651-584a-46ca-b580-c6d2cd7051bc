"""make_url_and_scraper_nullable_for_user_listings

Revision ID: 646455772bd3
Revises: f8301f9357b9
Create Date: 2025-06-25 10:27:56.032687

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '646455772bd3'
down_revision: Union[str, None] = 'f8301f9357b9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('listings', 'url',
               existing_type=sa.VARCHAR(length=500),
               nullable=True)
    op.alter_column('listings', 'note_about_size_sqm',
               existing_type=sa.VARCHAR(length=200),
               nullable=True)
    op.alter_column('listings', 'scraper',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
    op.drop_constraint(op.f('listings_url_key'), 'listings', type_='unique')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(op.f('listings_url_key'), 'listings', ['url'], postgresql_nulls_not_distinct=False)
    op.alter_column('listings', 'scraper',
               existing_type=sa.VARCHAR(length=50),
               nullable=False)
    op.alter_column('listings', 'note_about_size_sqm',
               existing_type=sa.VARCHAR(length=200),
               nullable=False)
    op.alter_column('listings', 'url',
               existing_type=sa.VARCHAR(length=500),
               nullable=False)
    # ### end Alembic commands ###
