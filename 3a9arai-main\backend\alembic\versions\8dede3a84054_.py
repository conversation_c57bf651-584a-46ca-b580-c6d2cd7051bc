"""empty message

Revision ID: 8dede3a84054
Revises: 7c1eab1a42c4
Create Date: 2025-06-02 17:37:48.137590

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8dede3a84054'
down_revision: Union[str, None] = '7c1eab1a42c4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('listings', sa.Column('website_source', sa.String(length=50), nullable=True))
    op.drop_column('listings', 'source_website')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('listings', sa.<PERSON>umn('source_website', sa.VARCHAR(length=50), autoincrement=False, nullable=True))
    op.drop_column('listings', 'website_source')
    # ### end Alembic commands ###
