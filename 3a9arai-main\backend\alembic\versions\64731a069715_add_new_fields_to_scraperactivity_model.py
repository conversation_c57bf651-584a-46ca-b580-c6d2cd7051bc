"""Add new fields to ScraperActivity model

Revision ID: 64731a069715
Revises: 7a32bdf41e27
Create Date: 2025-04-02 14:38:34.878302

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '64731a069715'
down_revision: Union[str, None] = '7a32bdf41e27'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('scraper_activity', sa.Column('progress_current', sa.Integer(), nullable=True))
    op.add_column('scraper_activity', sa.Column('progress_total', sa.Integer(), nullable=True))
    op.add_column('scraper_activity', sa.Column('progress_percent', sa.Float(), nullable=True))
    op.add_column('scraper_activity', sa.Column('error_details', sa.Text(), nullable=True))
    op.add_column('scraper_activity', sa.Column('runtime_seconds', sa.Integer(), nullable=True))
    op.add_column('scraper_activity', sa.Column('params', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('scraper_activity', 'params')
    op.drop_column('scraper_activity', 'runtime_seconds')
    op.drop_column('scraper_activity', 'error_details')
    op.drop_column('scraper_activity', 'progress_percent')
    op.drop_column('scraper_activity', 'progress_total')
    op.drop_column('scraper_activity', 'progress_current')
    # ### end Alembic commands ###
