"""Merge migration branches

Revision ID: 2b274126e163
Revises: 8790bdb296ab, clerk_integration_001
Create Date: 2025-05-23 19:23:52.099204

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2b274126e163'
down_revision: Union[str, None] = ('8790bdb296ab', 'clerk_integration_001')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
